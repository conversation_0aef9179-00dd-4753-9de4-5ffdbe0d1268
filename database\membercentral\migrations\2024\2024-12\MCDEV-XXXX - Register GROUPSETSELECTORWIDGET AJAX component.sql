/*
	MCDEV-XXXX - Register Multiple Group Set Selector methods for GROUPSETWIDGET

	This migration adds new methods to the existing GROUPSETWIDGET AJAX component for the Multiple Group Set Selector widget.
	The widget provides functionality to manage group sets in Member Settings with move up/down, add, edit, preview, and delete operations.
*/

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID INT;

	CREATE TABLE #ajaxComponentMethods (
		methodName VARCHAR(255) NOT NULL,
		resourceTypeFunctionID INT NOT NULL
	);

	DECLARE @adminViewRTFID INT;

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES
		('getAvailableAndSelectedGroupSetsJSON', @adminViewRTFID),
		('moveGroupSet', @adminViewRTFID),
		('removeGroupSet', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='GROUPSETWIDGET',
		@requestCFC='model.admin.common.modules.groupSetSelector.groupSetSelector',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

	PRINT 'Multiple Group Set Selector methods added to GROUPSETWIDGET successfully with componentID: ' + CAST(@componentID AS VARCHAR(10));

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	SELECT ERROR_MESSAGE();
END CATCH
GO
