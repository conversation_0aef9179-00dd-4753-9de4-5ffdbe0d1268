/*
	MCDEV-XXXX - Register GROUPSETSELECTORWIDGET AJAX component
	
	This migration registers the GROUPSETSELECTORWIDGET AJAX component for the Multiple Group Set Selector widget.
	The widget provides functionality to manage group sets in Member Settings with move up/down, add, edit, preview, and delete operations.
*/

BEGIN TRANSACTION;

	DECLARE @componentID INT;

	CREATE TABLE #ajaxComponentMethods (
		methodName VARCHAR(255) NOT NULL,
		resourceTypeFunctionID INT NOT NULL
	);

	DECLARE @adminViewRTFID INT;

	SELECT @adminViewRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Admin'),dbo.fn_getResourceFunctionID('View',dbo.fn_getResourceTypeID('Admin')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES 
		('getAvailableAndSelectedGroupSetsJSON', @adminViewRTFID),
		('moveGroupSet', @adminViewRTFID),
		('removeGroupSet', @adminViewRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='GROUPSETSELECTORWIDGET',
		@requestCFC='model.admin.common.modules.groupSetSelector.groupSetSelectorJSON',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

	PRINT 'GROUPSETSELECTORWIDGET AJAX component registered successfully with componentID: ' + CAST(@componentID AS VARCHAR(10));

COMMIT TRANSACTION;
