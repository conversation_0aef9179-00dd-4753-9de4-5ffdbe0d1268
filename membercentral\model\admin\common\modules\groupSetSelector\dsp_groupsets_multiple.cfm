<cfsavecontent variable="local.gsSelectorJS">
	<cfoutput>
	<script language="javascript">
		function loadGroupSetGrids_#arguments.selectorID#() {
			let loadGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let selectedGSListSource = $('##mc_SelectedGSList_#arguments.selectorID#').html();
					let selectedGSListTemplate = Handlebars.compile(selectedGSListSource);
					$('##selectedGSGridContainer_#arguments.selectorID#').html(selectedGSListTemplate(r));
					
					let availGSListSource = $('##mc_AvailGSList_#arguments.selectorID#').html();
					let availGSListTemplate = Handlebars.compile(availGSListSource);
					$('##availGSGridContainer_#arguments.selectorID#').html(availGSListTemplate(r));
					
					mcActivateTooltip($('##selectedGSGridContainer_#arguments.selectorID#'));
					mcActivateTooltip($('##availGSGridContainer_#arguments.selectorID#'));
					
					updateGroupSetCounts_#arguments.selectorID#(r);
				}
			};
			getGroupSetsJSON_#arguments.selectorID#(loadGSResult);
		}

		function updateGroupSetCounts_#arguments.selectorID#(data) {
			var selectedCount = data.arrselectedgroupsets ? data.arrselectedgroupsets.length : 0;
			var availableCount = data.arravailablegroupsets ? data.arravailablegroupsets.length : 0;
			
			$('##selectedGSGridContainer_#arguments.selectorID#').closest('.card').find('.selGSCount').text(selectedCount);
			$('##availGSGridContainer_#arguments.selectorID#').closest('.card').find('.availGSCount').text(availableCount);
			
			if (selectedCount > 0) {
				$('.gsCountSpan').removeClass('d-none');
				$('.gsCountLoadingSpan').addClass('d-none');
			}
		}

		function editGroupSet_#arguments.selectorID#(gsID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: gsID > 0 ? 'Edit Group Set' : 'Create a Group Set',
				iframe: true,
				contenturl: '#local.editGroupSetLink#&gsID=' + gsID,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'saveMemGroupSet_#arguments.selectorID#',
					extrabuttonlabel: 'Save'
				}
			});
		}

		function saveMemGroupSet_#arguments.selectorID#() {
			$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveGroupSet();
		}

		function previewGroupSet_#arguments.selectorID#(gsID) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				title: 'Preview Group Set',
				contenturl: '#local.previewGroupSetLink#&gsID=' + gsID,
				strmodalfooter: { showclose: true }
			});
		}

		function editClassification_#arguments.selectorID#(gsID, classificationID) {
			var srID = #arguments.siteResourceID#;
			MCModalUtils.showModal({
				title: classificationID > 0 ? 'Edit Classification' : 'Add Classification',
				contenturl: '#local.editClassificationLink#&classificationID=' + (classificationID || 0) + '&siteResourceID=' + srID + '&groupSetID=' + gsID,
				strmodalfooter: {
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'saveClassification_#arguments.selectorID#',
					extrabuttonlabel: 'Save'
				}
			});
		}

		function saveClassification_#arguments.selectorID#() {
			setTimeout(function() {
				loadGroupSetGrids_#arguments.selectorID#();
			}, 500);
		}

		function moveGroupSetUp_#arguments.selectorID#(classificationID) {
			let moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				}
			};
			TS_AJX('GROUPSETWIDGET','moveGroupSet',{classificationID: classificationID, direction: 'up', siteResourceID: #arguments.siteResourceID#},moveResult,moveResult,60000,moveResult);
		}

		function moveGroupSetDown_#arguments.selectorID#(classificationID) {
			let moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				}
			};
			TS_AJX('GROUPSETWIDGET','moveGroupSet',{classificationID: classificationID, direction: 'down', siteResourceID: #arguments.siteResourceID#},moveResult,moveResult,60000,moveResult);
		}

		function addGroupSetToSelected_#arguments.selectorID#(gsID) {
			editClassification_#arguments.selectorID#(gsID, 0);
		}

		function removeGroupSetFromSelected_#arguments.selectorID#(classificationID) {
			if (confirm('Are you sure you want to remove this group set from the selected list?')) {
				let removeResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadGroupSetGrids_#arguments.selectorID#();
					}
				};
				var objParams = { classificationID: classificationID };
				TS_AJX('ADMMEMBERSETTINGS','deleteClassification',objParams,removeResult,removeResult,10000,removeResult);
			}
		}

		function getGroupSetsJSON_#arguments.selectorID#(onCompleteFunc){
			TS_AJX('GROUPSETWIDGET','getAvailableAndSelectedGroupSetsJSON',{siteResourceID: #arguments.siteResourceID#, isLinked: #arguments.isLinked#},onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}

		$(function() {
			loadGroupSetGrids_#arguments.selectorID#();
		});
	</script>
	<style>
		##availGSAccordion_#arguments.selectorID# > .card { box-shadow: none; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnPreviewGS { padding-right: 0.3rem; padding-left: 0.3rem; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnEditGS { padding-right: 0.32rem; padding-left: 0.32rem; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnAddGS { padding-right: 0.4rem; padding-left: 0.4rem; }
		##gsGridContainer_#arguments.selectorID# .table td { vertical-align: middle; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gsSelectorJS)#">

<cfoutput>
<div class="row no-gutters" id="gsGridContainer_#arguments.selectorID#">
	<div class="col-12">
		<div class="card card-box">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-sm">
					Selected Group Sets
				</div>
				<span class="gsCountSpan small d-none"><span class="selGSCount pr-1"></span>selected</span>
				<span class="gsCountLoadingSpan small">loading..</span>
			</div>
			<div class="card-body p-0">
				<div id="selectedGSGridContainer_#arguments.selectorID#" style="height:#arguments.selectedGSGridHeight#px;overflow-y:auto;">
				</div>
				<div class="accordion" id="availGSAccordion_#arguments.selectorID#">
					<div class="card card-box rounded-bottom">
						<div class="card-header bg-light rounded-0" id="availHeading_#arguments.selectorID#">
							<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##availCollapse_#arguments.selectorID#" aria-expanded="false" aria-controls="availCollapse_#arguments.selectorID#">
								<span class="font-size-sm"><span class="availGSCount pr-1"></span>Additional Group Sets Available</span>
								<div class="d-flex align-items-center">
									<i class="fa-solid fa-caret-up font-size-xl"></i>
								</div>
							</button>
						</div>
						<div id="availCollapse_#arguments.selectorID#" class="collapse" aria-labelledby="availHeading_#arguments.selectorID#" data-parent="##availGSAccordion_#arguments.selectorID#">
							<div class="card-body p-0">
								<div class="d-flex align-items-center justify-content-between p-2 bg-secondary font-size-xs">
									<span class="font-italic">Choose From Available Group Sets</span>
									<a href="##" name="btnCreateGroupSet" onclick="editGroupSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Group Set">
										<i class="fa-regular fa-circle-plus fa-lg"></i>
									</a>
								</div>
								<div id="availGSGridContainer_#arguments.selectorID#" style="height:300px;overflow-y:auto;">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Handlebars Template for Selected Group Sets -->
<script id="mc_SelectedGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arrselectedgroupsets}}
		<table class="table table-sm table-borderless table-striped mb-0">
		{{##each arrselectedgroupsets}}
			<tr class="border-bottom">
				<cfset local.actionColWidthPct = 33>
				<cfset local.nameColWidthPct = 100 - local.actionColWidthPct>
				<td width="#local.nameColWidthPct#%" class="pl-2 font-size-sm">
					<span class="font-weight-bold">{{groupsetname}}</span>
					{{##if classificationname}}
						<br><small class="text-muted">{{classificationname}}</small>
					{{/if}}
				</td>
				<td width="#local.actionColWidthPct#%" class="text-right">
					<span class="d-inline-block">
						<a href="##" id="moveUpGSBtn_#arguments.selectorID#_{{classificationid}}"
							{{##compare @index '!=' 0}}
								class="btn btn-xs btn-outline-dark"
								onclick="$(this).tooltip('hide');moveGroupSetUp_#arguments.selectorID#({{classificationid}});return false;"
								data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Up"
							{{/compare}}
							{{##compare @index '==' 0}}
								class="btn btn-xs btn-outline-dark invisible"
							{{/compare}}
							>
							<i class="fa-solid fa-up"></i>
						</a>
						<a href="##" id="moveDownGSBtn_#arguments.selectorID#_{{classificationid}}"
							{{##compare (math @index "+" 1) '!=' ../arrselectedgroupsets.length}}
								class="btn btn-xs btn-outline-dark"
								onclick="$(this).tooltip('hide');moveGroupSetDown_#arguments.selectorID#({{classificationid}});return false;"
								data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Down"
							{{/compare}}
							{{##compare (math @index "+" 1) '==' ../arrselectedgroupsets.length}}
								class="btn btn-xs btn-outline-dark invisible"
							{{/compare}}
							>
							<i class="fa-solid fa-down"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');editGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-info btnEditGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Group Set">
							<i class="fa-solid fa-pencil"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');previewGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-primary btnPreviewGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Group Set">
							<i class="fa-solid fa-eye"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');editClassification_#arguments.selectorID#({{groupsetid}}, {{classificationid}});return false;" class="btn btn-xs btn-outline-warning" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Classification Settings">
							<i class="fa-solid fa-gear"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');removeGroupSetFromSelected_#arguments.selectorID#({{classificationid}});return false;" class="btn btn-xs btn-outline-danger" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Remove this Group Set From Selections">
							<i class="fa-solid fa-trash-can"></i>
						</a>
					</span>
				</td>
			</tr>
		{{/each}}
		</table>
	{{else}}
		<div class="text-center py-3">No Group Sets Selected.</div>
	{{/if}}
</script>

<!-- Handlebars Template for Available Group Sets -->
<script id="mc_AvailGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arravailablegroupsets}}
		{{##each arravailablegroupsets}}
			<div class="bg-secondary mb-3">
				<div><i class="fa-regular fa-tag font-size-md text-warning mr-2"></i><b class="font-size-sm">Available Group Sets</b></div>
				<ul class="list-group mt-2">
					<li class="list-group-item py-1">
						<div class="row no-gutters align-items-center">
							<div class="col font-size-sm">{{groupsetname}}</div>
							<div class="col-auto pl-2">
								<a href="##" id="btnAddGS_#arguments.selectorID#_{{groupsetid}}" onclick="$(this).tooltip('hide');addGroupSetToSelected_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-success btnAddGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Add this Group Set">
									<i class="fa-solid fa-plus"></i>
								</a>
								<a href="##" onclick="$(this).tooltip('hide');editGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-info btnEditGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Group Set">
									<i class="fa-solid fa-pencil"></i>
								</a>
								<a href="##" onclick="$(this).tooltip('hide');previewGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-primary btnPreviewGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Group Set">
									<i class="fa-solid fa-eye"></i>
								</a>
							</div>
						</div>
					</li>
				</ul>
			</div>
		{{/each}}
	{{else}}
		<div class="text-center py-3">No Group Sets Available.</div>
	{{/if}}
</script>
<script id="mc_AvailGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush list-group-#arguments.selectorID#">
		{{##each arravailablegroupsets}}
		<li class="d-flex align-items-center justify-content-between p-2">
			<!---<div class="custom-control custom-radio flex-grow-1">
				<input type="radio" id="radio_#arguments.selectorID#_{{groupsetid}}" name="radio_#arguments.selectorID#" value="{{groupsetid}}" class="custom-control-input">
				<label class="custom-control-label flex-grow-1 cursor-pointer" for="radio_#arguments.selectorID#_{{groupsetid}}">
					<span class="font-weight-bold">{{groupsetname}}</span>
				</label>
			</div>--->
			<div class="col font-size-sm">{{groupsetname}}</div>
			<div class="col-auto pl-2" role="group">
				<a href="##" onclick="$(this).tooltip('hide');previewGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-primary btnPreviewGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview Group Set">
					<i class="fa-solid fa-eye"></i>
				</a>
				<a href="##" onclick="$(this).tooltip('hide');editGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-info btnEditGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit Group Set">
					<i class="fa-solid fa-pencil"></i>
				</a>
			</div>
		</li>
		{{/each}}
		{{##unless arravailablegroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No group sets available
		</li>
		{{/unless}}
	</ul>
</script>
</cfoutput>
