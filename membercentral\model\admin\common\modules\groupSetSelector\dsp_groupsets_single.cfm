<cfsavecontent variable="local.gsSelectorJS">
	<cfoutput>
	<script language="javascript">
		function onChangeGroupSetSelection_#arguments.selectorID#(collapse) {
			var el = $('input[type="radio"][name="radio_#arguments.selectorID#"]:checked');
			var value = $(el).val();
			var idVal = $(el).attr("id");
			var labelVal = $("label[for='"+idVal+"']").text();
			$('###arguments.selectorID#').val(value).trigger('change');
			$('##selectedGSLabel_#arguments.selectorID#').text(labelVal).toggleClass('text-grey', (value == 0 ? true : false));
			$('.list-group.list-group-#arguments.selectorID# li.list-group-item').removeClass('bg-neutral-success');
			if(value > 0) $(el).closest('li.list-group-item').addClass('bg-neutral-success');
			
			if (collapse) {
				$('##collapse_#arguments.selectorID#').collapse('hide');
			}
		}

		function editGroupSet_#arguments.selectorID#(gsID) {
			var editURL = '#local.editGroupSetLink#&gsID=' + gsID;
			<cfif local.useInlinePreview>
				$('###arguments.inlinePreviewSectionID#_gsPreview').remove();
				$('###arguments.inlinePreviewSectionID#').after('<div id="#arguments.inlinePreviewSectionID#_gsPreview"></div>');
				$('###arguments.inlinePreviewSectionID#_gsPreview').load(editURL, function() {
					loadGroupSetGrids_#arguments.selectorID#(gsID);
				});
			<cfelse>
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: gsID > 0 ? 'Edit Group Set' : 'Create a Group Set',
					iframe: true,
					contenturl: editURL,
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'saveMemGroupSet_#arguments.selectorID#',
						extrabuttonlabel: 'Save'
					}
				});
			</cfif>
		}
		function saveMemGroupSet_#arguments.selectorID#(gsID) {
			$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveGroupSet();
		}

		function previewGroupSet_#arguments.selectorID#(gsID) {
			var previewURL = '#local.previewGroupSetLink#&gsID=' + gsID;
			<cfif local.useInlinePreview>
				$('###arguments.inlinePreviewSectionID#_gsPreview').remove();
				$('###arguments.inlinePreviewSectionID#').after('<div id="#arguments.inlinePreviewSectionID#_gsPreview"></div>');
				$('###arguments.inlinePreviewSectionID#_gsPreview').load(previewURL);
			<cfelse>
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					title: 'Preview Group Set',
					contenturl: previewURL,
					strmodalfooter: { showclose: true }
				});
			</cfif>
		}
		
		<cfif arguments.allowBlankOption>
			function clearGroupSetSelection_#arguments.selectorID#() {
				$("##radio_#arguments.selectorID#_0").prop("checked", true).change();
			}
		</cfif>
		function loadGroupSetGrids_#arguments.selectorID#(newGroupSetID) {
			let loadGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let gsListSource = $('##mc_GSList_#arguments.selectorID#').html();
					let gsListTemplate = Handlebars.compile(gsListSource);
					$('##gsGridContainer_#arguments.selectorID#').html(gsListTemplate(r));
					mcActivateTooltip($('##gsGridContainer_#arguments.selectorID#'));
					bindGSRadioChangeEvent_#arguments.selectorID#();

					/* select newly created group set or previous selection */
					var gsIDToSelect = (newGroupSetID && newGroupSetID) > 0 ? newGroupSetID : $('###arguments.selectorID#').val();
					$('##radio_#arguments.selectorID#_' + gsIDToSelect).prop("checked", true);
					<cfif not arguments.allowBlankOption>
						/* if blank option is not allowed and no selection is made yet, try to auto-select the first option */
						if ((gsIDToSelect || 0) == 0){
							var radioElms = $('input:not(##radio_#arguments.selectorID#_0)[type="radio"][name="radio_#arguments.selectorID#"]');
							if(radioElms.length > 0) $(radioElms[0]).prop("checked", true);
						}
					</cfif>
					onChangeGroupSetSelection_#arguments.selectorID#(false);
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadGroupSetGrids_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##gsGridContainer_#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##gsGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			<cfif len(arguments.getGroupSetDataFunc)>
				#arguments.getGroupSetDataFunc#(loadGSResult);
			<cfelse>
				getGroupSetsJSON_#arguments.selectorID#(loadGSResult);
			</cfif>
		}
		function getGroupSetsJSON_#arguments.selectorID#(onCompleteFunc){
			<cfif len(arguments.getGroupSetDataFunc)>
				#arguments.getGroupSetDataFunc#(onCompleteFunc);
			<cfelse>
				TS_AJX('GROUPSETWIDGET','getGroupSetsJSON',{},onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
			</cfif>
		}
		function bindGSRadioChangeEvent_#arguments.selectorID#(){
			$('input[type=radio][name=radio_#arguments.selectorID#]').off('change').change(function() {
				onChangeGroupSetSelection_#arguments.selectorID#(true);
			});
		}

		$(function() {
			loadGroupSetGrids_#arguments.selectorID#();
			<cfif local.useInlinePreview>
				if($('###arguments.inlinePreviewSectionID#_gsPreview').length == 0){
					$('###arguments.inlinePreviewSectionID#').after($('##mc_GSPreviewContainer_#arguments.selectorID#').html());
				}
			</cfif>
			$(document).on('shown.bs.collapse', '##collapse_#arguments.selectorID#', function (e) {
				let listGrpItem = $('ul.list-group-#arguments.selectorID#').find('li.list-group-item.bg-neutral-success');	
				let gsGridCard = $('##gsGridCard_#arguments.selectorID#');
				if (listGrpItem.length) {
					gsGridCard.scrollTop(gsGridCard.scrollTop() + listGrpItem.position().top - gsGridCard.height()/2 + listGrpItem.height()/2); 
				}
			});
		});
	</script>
	<style>
		##accordion_#arguments.selectorID# > .card { box-shadow: none; border-bottom: 1px solid rgba(122, 123, 151, 0.3); }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnPreviewGS { padding-right: 0.3rem; padding-left: 0.3rem; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnEditGS { padding-right: 0.32rem; padding-left: 0.32rem; }
		##gsGridContainer_#arguments.selectorID# .custom-control-label:before { border-color: ##a8a8a8; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gsSelectorJS)#">

<cfoutput>
<input type="hidden" name="#arguments.selectorID#" id="#arguments.selectorID#" value="#local.selectedGroupSetID#" />

<div class="row no-gutters">
	<div class="col-12">
		<div class="accordion" id="accordion_#arguments.selectorID#">
			<div class="card card-box rounded">
				<div class="card-header" id="heading_#arguments.selectorID#">
					<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
						<span id="selectedGSLabel_#arguments.selectorID#">#local.selectedGroupSetLabel#</span>
						<i class="fa-solid fa-caret-up font-size-xl"></i>
					</button>
				</div>
				<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##accordion_#arguments.selectorID#">
					<div class="card">
						<div class="card-header bg-light d-flex align-items-center justify-content-between font-size-xs">
							<span class="font-italic">Choose From Available Group Sets</span>
							<div class="d-flex align-items-center">
								<cfif arguments.allowBlankOption>
									<span class="ml-auto mr-4 px-2 btn-link cursor-pointer text-grey font-size-xs" onclick="clearGroupSetSelection_#arguments.selectorID#()">clear selection</span>
								</cfif>
								<a href="##" name="btnCreateGroupSet" onclick="editGroupSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Group Set">
									<i class="fa-regular fa-circle-plus fa-lg"></i>
								</a>
							</div>
						</div>
						<div id="gsGridCard_#arguments.selectorID#" class="card-body bg-secondary p-2" style="height:250px;overflow-y:auto;">
							<div class="d-none">
								<input type="radio" id="radio_#arguments.selectorID#_0" name="radio_#arguments.selectorID#" value="0" <cfif arguments.allowBlankOption and local.selectedGroupSetID eq 0>checked</cfif>>
								<label for="radio_#arguments.selectorID#_0">Choose Group Set</label>
							</div>
							<div id="gsGridContainer_#arguments.selectorID#"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<cfif local.useInlinePreview>
	<div id="mc_GSPreviewContainer_#arguments.selectorID#" class="d-none">
		<div id="#arguments.inlinePreviewSectionID#_gsPreview" class="mt-3"></div>
	</div>
</cfif>

<!-- Handlebars Template for Group Set List -->
<script id="mc_GSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush list-group-#arguments.selectorID#">
		{{##each arravailablegroupsets}}
		<li class="list-group-item d-flex align-items-center justify-content-between p-2">
			<div class="custom-control custom-radio flex-grow-1">
				<input type="radio" id="radio_#arguments.selectorID#_{{groupsetid}}" name="radio_#arguments.selectorID#" value="{{groupsetid}}" class="custom-control-input">
				<label class="custom-control-label flex-grow-1 cursor-pointer" for="radio_#arguments.selectorID#_{{groupsetid}}">
					<span class="font-weight-bold">{{groupsetname}}</span>
				</label>
			</div>
			<div class="col-auto pl-2" role="group">
				<a href="##" onclick="$(this).tooltip('hide');previewGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-primary btnPreviewGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview Group Set">
					<i class="fa-solid fa-eye"></i>
				</a>
				<a href="##" onclick="$(this).tooltip('hide');editGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-info btnEditGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit Group Set">
					<i class="fa-solid fa-pencil"></i>
				</a>
			</div>
		</li>
		{{/each}}
		{{##unless arravailablegroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No group sets available
		</li>
		{{/unless}}
	</ul>
</script>
</cfoutput>
