<!--- Test page for Multiple Group Set Selector Widget --->
<cfparam name="url.siteResourceID" default="1">
<cfparam name="url.isLinked" default="false">

<cfset local.objGroupSetSelector = CreateObject("component","groupSetSelector")>

<!--- Test regular group sets --->
<cfset local.regularWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
	siteID = 1,
	orgID = 1,
	selectorID = "testRegularGroupSets",
	siteResourceID = url.siteResourceID,
	isLinked = false,
	selectedGSGridHeight = 300
)>

<!--- Test linked group sets --->
<cfset local.linkedWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
	siteID = 1,
	orgID = 1,
	selectorID = "testLinkedGroupSets",
	siteResourceID = url.siteResourceID,
	isLinked = true,
	selectedGSGridHeight = 300
)>

<!DOCTYPE html>
<html>
<head>
	<title>Multiple Group Set Selector Widget Test</title>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/handlebars.js/4.7.7/handlebars.min.js"></script>
	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
	
	<!--- Include MemberCentral specific scripts --->
	<script>
		// Mock TS_AJX function for testing
		function TS_AJX(component, method, params, successCallback, errorCallback, timeout, timeoutCallback) {
			console.log('TS_AJX called:', component, method, params);
			
			// Mock response for testing
			var mockResponse = {
				success: "true",
				arrselectedgroupsets: [
					{
						classificationid: 1,
						classificationname: "Test Classification 1",
						groupsetid: 1,
						groupsetname: "Test Group Set 1",
						classificationorder: 1
					},
					{
						classificationid: 2,
						classificationname: "Test Classification 2",
						groupsetid: 2,
						groupsetname: "Test Group Set 2",
						classificationorder: 2
					}
				],
				arravailablegroupsets: [
					{
						groupsetid: 3,
						groupsetname: "Available Group Set 1"
					},
					{
						groupsetid: 4,
						groupsetname: "Available Group Set 2"
					}
				]
			};
			
			setTimeout(function() {
				if (successCallback) successCallback(mockResponse);
			}, 100);
		}
		
		// Mock MCModalUtils for testing
		var MCModalUtils = {
			showModal: function(options) {
				console.log('MCModalUtils.showModal called:', options);
				alert('Modal would open: ' + options.title);
			}
		};
		
		// Mock tooltip activation
		function mcActivateTooltip(element) {
			console.log('mcActivateTooltip called for:', element);
		}
	</script>
</head>
<body>
	<div class="container-fluid mt-4">
		<h1>Multiple Group Set Selector Widget Test</h1>
		
		<div class="row">
			<div class="col-md-6">
				<h3>Regular Group Sets (siteResourceID: <cfoutput>#url.siteResourceID#</cfoutput>)</h3>
				<cfoutput>#local.regularWidget.html#</cfoutput>
			</div>
			
			<div class="col-md-6">
				<h3>Linked Group Sets (siteResourceID: <cfoutput>#url.siteResourceID#</cfoutput>)</h3>
				<cfoutput>#local.linkedWidget.html#</cfoutput>
			</div>
		</div>
		
		<div class="mt-4">
			<h4>Test Parameters:</h4>
			<p>
				<a href="?siteResourceID=1&isLinked=false">Test with siteResourceID=1</a> | 
				<a href="?siteResourceID=2&isLinked=false">Test with siteResourceID=2</a>
			</p>
		</div>
	</div>
</body>
</html>
