<cfcomponent output="false">

	<cffunction name="getAvailableAndSelectedGroupSetsJSON" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.siteResourceID = arguments.event.getValue('siteResourceID', 0)>
		<cfset local.isLinked = arguments.event.getValue('isLinked', false)>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>

		<cfset local.returnStruct = {
			"success": true,
			"arrselectedgroupsets": [],
			"arravailablegroupsets": []
		}>

		<cftry>
			<!--- Get all group sets for the organization --->
			<cfset local.objMemberGroupSets = CreateObject("component","model.admin.memberGroupSets.memberGroupSets")>
			<cfset local.qryAllGroupSets = local.objMemberGroupSets.getGroupSets(orgID=local.orgID)>

			<!--- Get currently selected classifications --->
			<cfquery name="local.qrySelectedClassifications" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT c.classificationID, c.name as classificationName, c.groupSetID, c.classificationOrder,
					   mgs.groupSetName, c.allowSearch, c.showInSearchResults
				FROM dbo.ams_classifications c
				INNER JOIN dbo.ams_memberGroupSets mgs ON mgs.groupSetID = c.groupSetID
				WHERE c.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID#">
				<cfif local.isLinked>
					AND c.area = 'linkedrecord'
				<cfelse>
					AND c.area = 'details'
				</cfif>
				ORDER BY c.classificationOrder;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<!--- Build selected group sets array --->
			<cfloop query="local.qrySelectedClassifications">
				<cfset local.tmpSelected = {
					"classificationid": local.qrySelectedClassifications.classificationID,
					"classificationname": htmlEditFormat(local.qrySelectedClassifications.classificationName),
					"groupsetid": local.qrySelectedClassifications.groupSetID,
					"groupsetname": htmlEditFormat(local.qrySelectedClassifications.groupSetName),
					"classificationorder": local.qrySelectedClassifications.classificationOrder,
					"allowsearch": local.qrySelectedClassifications.allowSearch,
					"showinSearchResults": local.qrySelectedClassifications.showInSearchResults
				}>
				<cfset arrayAppend(local.returnStruct.arrselectedgroupsets, local.tmpSelected)>
			</cfloop>

			<!--- Build available group sets array (exclude already selected ones) --->
			<cfset local.selectedGroupSetIDs = "">
			<cfloop query="local.qrySelectedClassifications">
				<cfset local.selectedGroupSetIDs = listAppend(local.selectedGroupSetIDs, local.qrySelectedClassifications.groupSetID)>
			</cfloop>

			<cfloop query="local.qryAllGroupSets">
				<cfif NOT listFind(local.selectedGroupSetIDs, local.qryAllGroupSets.groupSetID)>
					<cfset local.tmpAvailable = {
						"groupsetid": local.qryAllGroupSets.groupSetID,
						"groupsetname": htmlEditFormat(local.qryAllGroupSets.groupSetName)
					}>
					<cfset arrayAppend(local.returnStruct.arravailablegroupsets, local.tmpAvailable)>
				</cfif>
			</cfloop>

			<cfcatch type="any">
				<cfset local.returnStruct = {
					"success": false,
					"message": "Error loading group sets: " & cfcatch.message,
					"arrselectedgroupsets": [],
					"arravailablegroupsets": []
				}>
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="moveGroupSet" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.classificationID = arguments.event.getValue('classificationID', 0)>
		<cfset local.direction = arguments.event.getValue('direction', '')>
		<cfset local.siteResourceID = arguments.event.getValue('siteResourceID', 0)>

		<cfset local.returnStruct = { "success": false, "message": "" }>

		<cftry>
			<cfif local.classificationID GT 0 AND len(local.direction) AND local.siteResourceID GT 0>
				<!--- Get current classification order --->
				<cfquery name="local.qryCurrentClassification" datasource="#application.dsn.membercentral.dsn#">
					SELECT classificationOrder, area
					FROM dbo.ams_classifications
					WHERE classificationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.classificationID#">
					AND siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID#">
				</cfquery>

				<cfif local.qryCurrentClassification.recordCount>
					<cfset local.currentOrder = local.qryCurrentClassification.classificationOrder>
					<cfset local.area = local.qryCurrentClassification.area>
					
					<cfif local.direction EQ "up">
						<cfset local.newOrder = local.currentOrder - 1>
					<cfelse>
						<cfset local.newOrder = local.currentOrder + 1>
					</cfif>

					<!--- Find the classification to swap with --->
					<cfquery name="local.qrySwapClassification" datasource="#application.dsn.membercentral.dsn#">
						SELECT classificationID
						FROM dbo.ams_classifications
						WHERE siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID#">
						AND area = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.area#">
						AND classificationOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newOrder#">
					</cfquery>

					<cfif local.qrySwapClassification.recordCount>
						<!--- Perform the swap --->
						<cfquery datasource="#application.dsn.membercentral.dsn#">
							UPDATE dbo.ams_classifications 
							SET classificationOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newOrder#">
							WHERE classificationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.classificationID#">
						</cfquery>

						<cfquery datasource="#application.dsn.membercentral.dsn#">
							UPDATE dbo.ams_classifications 
							SET classificationOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.currentOrder#">
							WHERE classificationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySwapClassification.classificationID#">
						</cfquery>

						<cfset local.returnStruct.success = true>
						<cfset local.returnStruct.message = "Group set moved successfully">
					<cfelse>
						<cfset local.returnStruct.message = "Cannot move group set in that direction">
					</cfif>
				<cfelse>
					<cfset local.returnStruct.message = "Classification not found">
				</cfif>
			<cfelse>
				<cfset local.returnStruct.message = "Invalid parameters">
			</cfif>

			<cfcatch type="any">
				<cfset local.returnStruct.message = "Error moving group set: " & cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="removeGroupSet" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.classificationID = arguments.event.getValue('classificationID', 0)>
		<cfset local.siteResourceID = arguments.event.getValue('siteResourceID', 0)>

		<cfset local.returnStruct = { "success": false, "message": "" }>

		<cftry>
			<cfif local.classificationID GT 0 AND local.siteResourceID GT 0>
				<!--- Delete the classification --->
				<cfquery datasource="#application.dsn.membercentral.dsn#">
					DELETE FROM dbo.ams_classifications
					WHERE classificationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.classificationID#">
					AND siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID#">
				</cfquery>

				<!--- Reorder remaining classifications --->
				<cfquery datasource="#application.dsn.membercentral.dsn#">
					EXEC dbo.ams_reorderClassifications @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID#">
				</cfquery>

				<cfset local.returnStruct.success = true>
				<cfset local.returnStruct.message = "Group set removed successfully">
			<cfelse>
				<cfset local.returnStruct.message = "Invalid parameters">
			</cfif>

			<cfcatch type="any">
				<cfset local.returnStruct.message = "Error removing group set: " & cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

</cfcomponent>
